use anchor_lang::prelude::*;
use super::instructions::*;
use super::state::*;
use super::errors::MultisigError;
use super::constants::*;

/// Process the initialization of a new multisig account
pub fn process_initialize_multisig(
    ctx: Context<InitializeMultiSig>,
    name: [u8; 32],
    signers: Vec<RoleSigner>,
    threshold: u8,
    role_based_approval: bool,
    min_approvals_per_role: Vec<RoleApproval>,
) -> Result<()> {
    let multisig_account = &mut ctx.accounts.multisig_account;
    let payment_account = &ctx.accounts.payment_account;

    // Initialize the multisig account
    multisig_account.initialize(
        name,
        payment_account.key(),
        signers,
        threshold,
        role_based_approval,
        min_approvals_per_role,
    )?;

    msg!("Multisig account initialized with {} signers and threshold {}",
        multisig_account.signers.len(),
        multisig_account.threshold
    );

    Ok(())
}

/// Process a transaction proposal
pub fn process_propose_transaction(
    ctx: Context<ProposeTransaction>,
    amount: u64,
    expires_in_seconds: i64,
    memo: [u8; 32],
) -> Result<()> {
    let multisig_account = &mut ctx.accounts.multisig_account;
    let proposer = ctx.accounts.proposer.key();
    let destination = ctx.accounts.destination.key();

    // Propose the transaction
    let tx_id = multisig_account.propose_transaction(
        proposer,
        destination,
        amount,
        expires_in_seconds,
        memo,
    )?;

    msg!("Transaction proposed with ID: {}", tx_id);

    Ok(())
}

/// Process a transaction approval
pub fn process_approve_transaction(
    ctx: Context<ApproveTransaction>,
    tx_id: u64,
    memo: [u8; 32],
) -> Result<()> {
    let multisig_account = &mut ctx.accounts.multisig_account;
    let approver = ctx.accounts.approver.key();

    // Approve the transaction
    multisig_account.approve_transaction(tx_id, approver, memo)?;

    msg!("Transaction {} approved by {}", tx_id, approver);

    Ok(())
}

/// Process a transaction execution
pub fn process_execute_transaction(
    ctx: Context<ExecuteTransaction>,
    tx_id: u64,
    memo: [u8; 32],
) -> Result<()> {
    let multisig_account = &mut ctx.accounts.multisig_account;
    let executor = ctx.accounts.executor.key();
    let payment_account = &ctx.accounts.payment_account;
    let destination = &ctx.accounts.destination;

    // Find the pending transaction
    let tx_index = multisig_account.pending_transactions
        .iter()
        .position(|tx| tx.tx_id == tx_id && tx.status == TransactionStatus::Pending)
        .ok_or(error!(MultisigError::TransactionNotFound))?;

    let tx = &multisig_account.pending_transactions[tx_index];

    // Verify destination matches
    require!(
        tx.destination == destination.key(),
        MultisigError::InvalidPaymentAccount
    );

    // Check if transaction has enough approvals
    require!(
        multisig_account.has_enough_approvals(tx_id)?,
        MultisigError::NotEnoughApprovals
    );

    // Check velocity limits before executing the transaction
    let amount = tx.amount;
    multisig_account.check_velocity_limits(amount)?;

    // Transfer lamports from payment account to destination
    **payment_account.to_account_info().try_borrow_mut_lamports()? = payment_account
        .to_account_info()
        .lamports()
        .checked_sub(amount)
        .unwrap();

    **destination.try_borrow_mut_lamports()? = destination
        .lamports()
        .checked_add(amount)
        .unwrap();

    // Mark the transaction as executed
    multisig_account.execute_transaction(tx_id, executor, memo)?;

    msg!("Transaction {} executed by {}", tx_id, executor);

    Ok(())
}

/// Process a transaction cancellation
pub fn process_cancel_transaction(
    ctx: Context<CancelTransaction>,
    tx_id: u64,
    memo: [u8; 32],
) -> Result<()> {
    let multisig_account = &mut ctx.accounts.multisig_account;
    let canceller = ctx.accounts.canceller.key();

    // Cancel the transaction
    multisig_account.cancel_transaction(tx_id, canceller, memo)?;

    msg!("Transaction {} cancelled by {}", tx_id, canceller);

    Ok(())
}

/// Process setting velocity controls
pub fn process_set_velocity_control(
    ctx: Context<SetVelocityControl>,
    hourly_limit: u64,
    daily_limit: u64,
    weekly_limit: u64,
    monthly_limit: u64,
) -> Result<()> {
    let multisig_account = &mut ctx.accounts.multisig_account;
    let authority = ctx.accounts.authority.key();

    // Validate that at least one limit is set
    if hourly_limit == 0 && daily_limit == 0 && weekly_limit == 0 && monthly_limit == 0 {
        return Err(error!(MultisigError::InvalidVelocityControl));
    }

    // Set velocity controls
    multisig_account.set_velocity_control(
        hourly_limit,
        daily_limit,
        weekly_limit,
        monthly_limit,
    )?;

    msg!(
        "Velocity controls set by {}: hourly={}, daily={}, weekly={}, monthly={}",
        authority,
        hourly_limit,
        daily_limit,
        weekly_limit,
        monthly_limit
    );

    Ok(())
}

/// Process removing velocity controls
pub fn process_remove_velocity_control(
    ctx: Context<RemoveVelocityControl>,
) -> Result<()> {
    let multisig_account = &mut ctx.accounts.multisig_account;
    let authority = ctx.accounts.authority.key();

    // Remove velocity controls
    multisig_account.remove_velocity_control();

    msg!("Velocity controls removed by {}", authority);

    Ok(())
}
