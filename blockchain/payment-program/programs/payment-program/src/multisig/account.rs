use anchor_lang::prelude::*;
use super::constants::*;
use super::errors::MultisigError;
use super::state::*;
use std::collections::BTreeMap;

impl MultiSigAccount {
    pub fn space(
        max_signers: usize,
        max_pending_txs: usize,
        max_signatures: usize,
        max_logs: usize,
    ) -> usize {
        // Discriminator
        let base_size = 8 +
                       // name: [u8; 32]
                       32 +
                       // payment_account: Pubkey
                       32 +
                       // threshold: u8
                       1 +
                       // role_based_approval: bool
                       1 +
                       // next_tx_id: u64
                       8 +
                       // created_at: i64
                       8;

        // Vec<RoleSigner>
        let signers_size = 4 + // Vec length
                          (32 + 1) * max_signers; // Pubkey + role (u8)

        // Vec<RoleApproval>
        let role_approvals_size = 4 + // Vec length
                                 (1 + 1) * 4; // role (u8) + min_approvals (u8) * 4 roles

        // Vec<Pubkey> for signatures
        let signature_size = 4 + // Vec length
                            32 * max_signatures; // Pubkey

        // Vec<SignatureLog>
        let log_size = 4 + // Vec length
                      (32 + // signer: Pubkey
                       1 +  // role: u8
                       1 +  // action: u8
                       8 +  // timestamp: i64
                       32   // memo: [u8; 32]
                      ) * max_logs;

        // Vec<PendingTransaction>
        let pending_tx_size = 4 + // Vec length
                             (8 +  // tx_id: u64
                              32 + // proposer: Pubkey
                              32 + // destination: Pubkey
                              8 +  // amount: u64
                              8 +  // created_at: i64
                              8 +  // expires_at: i64
                              1 +  // status: TransactionStatus (enum)
                              signature_size + // signatures: Vec<Pubkey>
                              log_size // logs: Vec<SignatureLog>
                             ) * max_pending_txs;

        // Option<VelocityControl> size
        let velocity_control_size = 1 + // Option discriminator
                                   (8 + // hourly_limit: u64
                                    8 + // daily_limit: u64
                                    8 + // weekly_limit: u64
                                    8 + // monthly_limit: u64
                                    8 + // current_hour_spent: u64
                                    8 + // current_day_spent: u64
                                    8 + // current_week_spent: u64
                                    8 + // current_month_spent: u64
                                    8 + // last_hour_reset: i64
                                    8 + // last_day_reset: i64
                                    8 + // last_week_reset: i64
                                    8   // last_month_reset: i64
                                   );

        // Calculate total size
        let total_size = base_size + signers_size + role_approvals_size + pending_tx_size + velocity_control_size;

        // Ensure we don't exceed the limit (10240 bytes)
        std::cmp::min(total_size, 10000)
    }

    pub fn initialize(
        &mut self,
        name: [u8; 32],
        payment_account: Pubkey,
        signers: Vec<RoleSigner>,
        threshold: u8,
        role_based_approval: bool,
        min_approvals_per_role: Vec<RoleApproval>,
    ) -> Result<()> {
        require!(!signers.is_empty(), MultisigError::NoSigners);
        require!(threshold > 0, MultisigError::InvalidThreshold);
        require!(
            threshold as usize <= signers.len(),
            MultisigError::ThresholdTooHigh
        );

        if role_based_approval {
            require!(
                !min_approvals_per_role.is_empty(),
                MultisigError::NoRoleApprovals
            );
        }

        self.name = name;
        self.payment_account = payment_account;
        self.signers = signers;
        self.threshold = threshold;
        self.role_based_approval = role_based_approval;
        self.min_approvals_per_role = min_approvals_per_role;
        self.pending_transactions = Vec::new();
        self.next_tx_id = 1;
        self.created_at = Clock::get()?.unix_timestamp;
        self.velocity_control = None; // Initialize with no velocity controls

        Ok(())
    }

    pub fn is_signer(&self, pubkey: &Pubkey) -> bool {
        self.signers.iter().any(|s| s.signer == *pubkey)
    }

    pub fn get_signer_role(&self, pubkey: &Pubkey) -> Option<u8> {
        self.signers
            .iter()
            .find(|s| s.signer == *pubkey)
            .map(|s| s.role)
    }

    /// Set velocity control limits for this multisig account
    pub fn set_velocity_control(
        &mut self,
        hourly_limit: u64,
        daily_limit: u64,
        weekly_limit: u64,
        monthly_limit: u64,
    ) -> Result<()> {
        let now = Clock::get()?.unix_timestamp;

        self.velocity_control = Some(VelocityControl {
            hourly_limit,
            daily_limit,
            weekly_limit,
            monthly_limit,
            current_hour_spent: 0,
            current_day_spent: 0,
            current_week_spent: 0,
            current_month_spent: 0,
            last_hour_reset: now,
            last_day_reset: now,
            last_week_reset: now,
            last_month_reset: now,
        });

        Ok(())
    }

    /// Remove velocity controls from this multisig account
    pub fn remove_velocity_control(&mut self) {
        self.velocity_control = None;
    }
}
