use anchor_lang::prelude::*;

#[error_code]
pub enum MultisigError {
    #[msg("No signers provided for multisig account")]
    NoSigners,

    #[msg("Invalid threshold, must be greater than 0")]
    InvalidThreshold,

    #[msg("Threshold is higher than the number of signers")]
    ThresholdTooHigh,

    #[msg("Role-based approval enabled but no role approvals specified")]
    NoRoleApprovals,

    #[msg("Unauthorized signer")]
    UnauthorizedSigner,

    #[msg("Invalid amount, must be greater than 0")]
    InvalidAmount,

    #[msg("Too many pending transactions")]
    TooManyPendingTransactions,

    #[msg("Transaction not found")]
    TransactionNotFound,

    #[msg("Transaction has expired")]
    TransactionExpired,

    #[msg("Signer has already approved this transaction")]
    AlreadyApproved,

    #[msg("Unauthorized to cancel this transaction")]
    UnauthorizedCancellation,

    #[msg("Not enough approvals to execute this transaction")]
    NotEnoughApprovals,

    #[msg("Required role approval not met")]
    RoleApprovalNotMet,

    #[msg("Invalid role assignment")]
    InvalidRoleAssignment,

    #[msg("Too many logs for this transaction")]
    TooManyLogs,

    #[msg("Invalid payment account")]
    InvalidPaymentAccount,

    #[msg("Hourly spending limit exceeded")]
    HourlyLimitExceeded,

    #[msg("Daily spending limit exceeded")]
    DailyLimitExceeded,

    #[msg("Weekly spending limit exceeded")]
    WeeklyLimitExceeded,

    #[msg("Monthly spending limit exceeded")]
    MonthlyLimitExceeded,

    #[msg("Invalid velocity control settings")]
    InvalidVelocityControl,
}
