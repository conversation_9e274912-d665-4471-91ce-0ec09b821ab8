{"address": "CDJfjkmASLweH8BPuBAMRufJGCsHSgwhJcZXfjgZ7Fne", "metadata": {"name": "payment_program", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "approve_transaction", "discriminator": [224, 39, 88, 181, 36, 59, 155, 122], "accounts": [{"name": "approver", "docs": ["The user approving the transaction (must be a signer on the multisig)"], "writable": true, "signer": true}, {"name": "multisig_account", "docs": ["The multisig account"], "writable": true}], "args": [{"name": "tx_id", "type": "u64"}, {"name": "memo", "type": {"array": ["u8", 32]}}]}, {"name": "cancel_transaction", "discriminator": [65, 191, 19, 127, 230, 26, 214, 142], "accounts": [{"name": "canceller", "docs": ["The user cancelling the transaction (must be a signer on the multisig)"], "writable": true, "signer": true}, {"name": "multisig_account", "docs": ["The multisig account"], "writable": true}], "args": [{"name": "tx_id", "type": "u64"}, {"name": "memo", "type": {"array": ["u8", 32]}}]}, {"name": "deposit", "discriminator": [242, 35, 198, 137, 82, 225, 242, 182], "accounts": [{"name": "user", "writable": true, "signer": true}, {"name": "payment_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 97, 121, 109, 101, 110, 116, 45, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "execute_transaction", "discriminator": [231, 173, 49, 91, 235, 24, 68, 19], "accounts": [{"name": "executor", "docs": ["The user executing the transaction (must be a signer on the multisig)"], "writable": true, "signer": true}, {"name": "multisig_account", "docs": ["The multisig account"], "writable": true}, {"name": "payment_account", "docs": ["The payment account controlled by the multisig"], "writable": true}, {"name": "destination", "docs": ["The destination account for the transfer"], "writable": true}, {"name": "system_program", "docs": ["System program for transferring funds"], "address": "11111111111111111111111111111111"}], "args": [{"name": "tx_id", "type": "u64"}, {"name": "memo", "type": {"array": ["u8", 32]}}]}, {"name": "initialize", "discriminator": [175, 175, 109, 31, 13, 152, 155, 237], "accounts": [], "args": []}, {"name": "initialize_account", "discriminator": [74, 115, 99, 93, 197, 69, 103, 7], "accounts": [{"name": "user", "writable": true, "signer": true}, {"name": "payment_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 97, 121, 109, 101, 110, 116, 45, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "initialize_fee_vault", "discriminator": [185, 140, 228, 234, 79, 203, 252, 50], "accounts": [{"name": "authority", "writable": true, "signer": true}, {"name": "fee_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [102, 101, 101, 45, 118, 97, 117, 108, 116]}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "fee_rate_bps", "type": "u16"}]}, {"name": "initialize_multisig", "discriminator": [220, 130, 117, 21, 27, 227, 78, 213], "accounts": [{"name": "creator", "docs": ["The user creating the multisig account"], "writable": true, "signer": true}, {"name": "multisig_account", "docs": ["The multisig account to be created"], "writable": true, "signer": true}, {"name": "payment_account", "docs": ["The payment account this multisig will control"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 97, 121, 109, 101, 110, 116, 45, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "multisig_account"}]}}, {"name": "system_program", "docs": ["System program for account creation"], "address": "11111111111111111111111111111111"}], "args": [{"name": "name", "type": {"array": ["u8", 32]}}, {"name": "signers", "type": {"vec": {"defined": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}}}, {"name": "threshold", "type": "u8"}, {"name": "role_based_approval", "type": "bool"}, {"name": "min_approvals_per_role", "type": {"vec": {"defined": {"name": "RoleApproval"}}}}]}, {"name": "propose_transaction", "discriminator": [35, 204, 169, 240, 74, 70, 31, 236], "accounts": [{"name": "proposer", "docs": ["The user proposing the transaction (must be a signer on the multisig)"], "writable": true, "signer": true}, {"name": "multisig_account", "docs": ["The multisig account"], "writable": true}, {"name": "destination", "docs": ["The destination account for the transfer"]}], "args": [{"name": "amount", "type": "u64"}, {"name": "expires_in_seconds", "type": "i64"}, {"name": "memo", "type": {"array": ["u8", 32]}}]}, {"name": "remove_velocity_control", "discriminator": [133, 145, 63, 63, 72, 184, 142, 101], "accounts": [{"name": "authority", "docs": ["The user removing velocity controls (must be a signer on the multisig)"], "writable": true, "signer": true}, {"name": "multisig_account", "docs": ["The multisig account"], "writable": true}], "args": []}, {"name": "set_account_settings", "discriminator": [56, 13, 72, 157, 98, 24, 237, 129], "accounts": [{"name": "owner", "writable": true, "signer": true}, {"name": "payment_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 97, 121, 109, 101, 110, 116, 45, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "owner"}]}}], "args": [{"name": "is_frozen", "type": {"option": "bool"}}, {"name": "spend_limit", "type": {"option": "u64"}}]}, {"name": "set_velocity_control", "discriminator": [87, 36, 74, 252, 74, 1, 175, 138], "accounts": [{"name": "authority", "docs": ["The user setting velocity controls (must be a signer on the multisig)"], "writable": true, "signer": true}, {"name": "multisig_account", "docs": ["The multisig account"], "writable": true}], "args": [{"name": "hourly_limit", "type": "u64"}, {"name": "daily_limit", "type": "u64"}, {"name": "weekly_limit", "type": "u64"}, {"name": "monthly_limit", "type": "u64"}]}, {"name": "transfer", "discriminator": [163, 52, 200, 231, 140, 3, 69, 186], "accounts": [{"name": "user", "writable": true, "signer": true}, {"name": "payment_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 97, 121, 109, 101, 110, 116, 45, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "recipient_account", "writable": true}, {"name": "fee_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [102, 101, 101, 45, 118, 97, 117, 108, 116]}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "withdraw", "discriminator": [183, 18, 70, 156, 148, 109, 161, 34], "accounts": [{"name": "user", "writable": true, "signer": true}, {"name": "payment_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 97, 121, 109, 101, 110, 116, 45, 97, 99, 99, 111, 117, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "fee_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [102, 101, 101, 45, 118, 97, 117, 108, 116]}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "withdraw_fees", "discriminator": [198, 212, 171, 109, 144, 215, 174, 89], "accounts": [{"name": "authority", "writable": true, "signer": true}, {"name": "fee_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [102, 101, 101, 45, 118, 97, 117, 108, 116]}]}}], "args": [{"name": "amount", "type": "u64"}]}], "accounts": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "discriminator": [192, 178, 69, 232, 58, 149, 157, 132]}, {"name": "MultiSigAccount", "discriminator": [77, 179, 91, 145, 94, 229, 133, 46]}, {"name": "PaymentAccount", "discriminator": [47, 239, 218, 78, 43, 193, 1, 61]}], "errors": [{"code": 6000, "name": "NoSigners", "msg": "No signers provided for multisig account"}, {"code": 6001, "name": "InvalidThreshold", "msg": "Invalid threshold, must be greater than 0"}, {"code": 6002, "name": "T<PERSON><PERSON>oldTooHigh", "msg": "Threshold is higher than the number of signers"}, {"code": 6003, "name": "NoRoleApprovals", "msg": "Role-based approval enabled but no role approvals specified"}, {"code": 6004, "name": "Unauthorized<PERSON><PERSON><PERSON>", "msg": "Unauthorized signer"}, {"code": 6005, "name": "InvalidAmount", "msg": "Invalid amount, must be greater than 0"}, {"code": 6006, "name": "TooManyPendingTransactions", "msg": "Too many pending transactions"}, {"code": 6007, "name": "TransactionNotFound", "msg": "Transaction not found"}, {"code": 6008, "name": "TransactionExpired", "msg": "Transaction has expired"}, {"code": 6009, "name": "AlreadyApproved", "msg": "Signer has already approved this transaction"}, {"code": 6010, "name": "UnauthorizedCancellation", "msg": "Unauthorized to cancel this transaction"}, {"code": 6011, "name": "NotEnoughApprovals", "msg": "Not enough approvals to execute this transaction"}, {"code": 6012, "name": "RoleApprovalNotMet", "msg": "Required role approval not met"}, {"code": 6013, "name": "InvalidRoleAssignment", "msg": "Invalid role assignment"}, {"code": 6014, "name": "TooManyLogs", "msg": "Too many logs for this transaction"}, {"code": 6015, "name": "InvalidPaymentAccount", "msg": "Invalid payment account"}, {"code": 6016, "name": "HourlyLimitExceeded", "msg": "Hourly spending limit exceeded"}, {"code": 6017, "name": "DailyLimitExceeded", "msg": "Daily spending limit exceeded"}, {"code": 6018, "name": "WeeklyLimitExceeded", "msg": "Weekly spending limit exceeded"}, {"code": 6019, "name": "MonthlyLimitExceeded", "msg": "Monthly spending limit exceeded"}, {"code": 6020, "name": "InvalidVelocityControl", "msg": "Invalid velocity control settings"}], "types": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "pubkey"}, {"name": "fee_balance", "type": "u64"}, {"name": "fee_rate_bps", "type": "u16"}, {"name": "created_at", "type": "i64"}]}}, {"name": "MultiSigAccount", "docs": ["A multi-signature account that requires multiple approvals for transactions"], "type": {"kind": "struct", "fields": [{"name": "name", "docs": ["Name of this multi-sig account (for identification)"], "type": {"array": ["u8", 32]}}, {"name": "payment_account", "docs": ["The payment account this multi-sig controls"], "type": "pubkey"}, {"name": "signers", "docs": ["All authorized signers with their roles"], "type": {"vec": {"defined": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}}}, {"name": "threshold", "docs": ["Number of signatures required to approve a transaction"], "type": "u8"}, {"name": "role_based_approval", "docs": ["Whether role-based approval is required"], "type": "bool"}, {"name": "min_approvals_per_role", "docs": ["Minimum approvals required from each role (if role_based_approval is true)"], "type": {"vec": {"defined": {"name": "RoleApproval"}}}}, {"name": "pending_transactions", "docs": ["Pending transactions awaiting approval"], "type": {"vec": {"defined": {"name": "PendingTransaction"}}}}, {"name": "next_tx_id", "docs": ["Next transaction ID to assign"], "type": "u64"}, {"name": "created_at", "docs": ["When this account was created"], "type": "i64"}, {"name": "velocity_control", "docs": ["Velocity control settings for spending limits (optional)"], "type": {"option": {"defined": {"name": "VelocityControl"}}}}]}}, {"name": "PaymentAccount", "type": {"kind": "struct", "fields": [{"name": "owner", "type": "pubkey"}, {"name": "balance", "type": "u64"}, {"name": "created_at", "type": "i64"}, {"name": "is_frozen", "type": "bool"}, {"name": "spend_limit", "type": "u64"}, {"name": "spent_today", "type": "u64"}, {"name": "last_spend_reset", "type": "i64"}]}}, {"name": "PendingTransaction", "docs": ["A pending transaction awaiting signatures"], "type": {"kind": "struct", "fields": [{"name": "tx_id", "docs": ["Unique identifier for this transaction"], "type": "u64"}, {"name": "proposer", "docs": ["The transaction proposer"], "type": "pubkey"}, {"name": "destination", "docs": ["The destination account for the transfer"], "type": "pubkey"}, {"name": "amount", "docs": ["The amount to transfer"], "type": "u64"}, {"name": "created_at", "docs": ["When the transaction was created"], "type": "i64"}, {"name": "expires_at", "docs": ["When the transaction expires"], "type": "i64"}, {"name": "status", "docs": ["Current status of the transaction"], "type": {"defined": {"name": "TransactionStatus"}}}, {"name": "signatures", "docs": ["Collected signatures (public keys of signers who approved)"], "type": {"vec": "pubkey"}}, {"name": "logs", "docs": ["Logs of all actions related to this transaction"], "type": {"vec": {"defined": {"name": "SignatureLog"}}}}]}}, {"name": "RoleApproval", "docs": ["Role approval requirement"], "type": {"kind": "struct", "fields": [{"name": "role", "docs": ["The role ID"], "type": "u8"}, {"name": "min_approvals", "docs": ["Minimum number of approvals required from this role"], "type": "u8"}]}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "docs": ["A signer with an assigned role"], "type": {"kind": "struct", "fields": [{"name": "signer", "docs": ["The signer's public key"], "type": "pubkey"}, {"name": "role", "docs": ["The role assigned to this signer"], "type": "u8"}]}}, {"name": "SignatureLog", "docs": ["A log entry for a signature action"], "type": {"kind": "struct", "fields": [{"name": "signer", "docs": ["The signer who performed the action"], "type": "pubkey"}, {"name": "role", "docs": ["The role of the signer"], "type": "u8"}, {"name": "action", "docs": ["The action performed (propose, approve, cancel, execute)"], "type": "u8"}, {"name": "timestamp", "docs": ["When the action was performed"], "type": "i64"}, {"name": "memo", "docs": ["Optional memo provided with the action"], "type": {"array": ["u8", 32]}}]}}, {"name": "TransactionStatus", "docs": ["Status of a pending transaction"], "type": {"kind": "enum", "variants": [{"name": "Pending"}, {"name": "Executed"}, {"name": "Cancelled"}, {"name": "Expired"}]}}, {"name": "VelocityControl", "docs": ["Velocity control settings for spending limits over time periods"], "type": {"kind": "struct", "fields": [{"name": "hourly_limit", "docs": ["Maximum amount that can be spent per hour (0 = no limit)"], "type": "u64"}, {"name": "daily_limit", "docs": ["Maximum amount that can be spent per day (0 = no limit)"], "type": "u64"}, {"name": "weekly_limit", "docs": ["Maximum amount that can be spent per week (0 = no limit)"], "type": "u64"}, {"name": "monthly_limit", "docs": ["Maximum amount that can be spent per month (0 = no limit)"], "type": "u64"}, {"name": "current_hour_spent", "docs": ["Amount spent in the current hour"], "type": "u64"}, {"name": "current_day_spent", "docs": ["Amount spent in the current day"], "type": "u64"}, {"name": "current_week_spent", "docs": ["Amount spent in the current week"], "type": "u64"}, {"name": "current_month_spent", "docs": ["Amount spent in the current month"], "type": "u64"}, {"name": "last_hour_reset", "docs": ["Timestamp of last hour reset"], "type": "i64"}, {"name": "last_day_reset", "docs": ["Timestamp of last day reset"], "type": "i64"}, {"name": "last_week_reset", "docs": ["Timestamp of last week reset"], "type": "i64"}, {"name": "last_month_reset", "docs": ["Timestamp of last month reset"], "type": "i64"}]}}]}