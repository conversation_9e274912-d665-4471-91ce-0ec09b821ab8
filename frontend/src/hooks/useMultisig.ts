import { useState, useCallback } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { PublicKey, Keypair, SystemProgram } from '@solana/web3.js';
import { Program } from '@coral-xyz/anchor';
import { setupProgram, programUtils, ROLES } from '../utils/anchor';
import { type MultiSigAccount, type RoleSigner, type RoleApproval } from '../types/anchor';

export const useMultisig = () => {
  const { publicKey, signTransaction, signAllTransactions } = useWallet();
  // const { connection } = useConnection();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get program instance
  const getProgram = useCallback((): Program<any> | null => {
    if (!publicKey || !signTransaction || !signAllTransactions) return null;
    
    const wallet = {
      publicKey,
      signTransaction,
      signAllTransactions,
    };
    
    return setupProgram(wallet);
  }, [publicKey, signTransaction, signAllTransactions]);

  // Create a new multisig account
  const createMultisig = useCallback(async (
    name: string,
    signers: { publicKey: string; role: number }[],
    threshold: number,
    roleBasedApproval: boolean = false,
    minApprovalsPerRole: { role: number; minApprovals: number }[] = []
  ) => {
    const program = getProgram();
    if (!program || !publicKey) {
      throw new Error('Wallet not connected');
    }

    setLoading(true);
    setError(null);

    try {
      // Generate new keypair for multisig account
      const multisigKeypair = Keypair.generate();
      // Derive PDA for payment account
      const [paymentAccountPda] = PublicKey.findProgramAddressSync(
        [Buffer.from('payment-account'), multisigKeypair.publicKey.toBuffer()],
        program.programId
      );
      
      // Convert signers to RoleSigner format
      const roleSigners: RoleSigner[] = signers.map(s => ({
        signer: new PublicKey(s.publicKey),
        role: s.role,
      }));

      // Convert role approvals
      const roleApprovals: RoleApproval[] = minApprovalsPerRole.map(r => ({
        role: r.role,
        minApprovals: r.minApprovals,
      }));

      // Convert name to bytes
      const nameBytes = programUtils.stringToBytes(name, 32);

      const tx = await program.methods
        .initializeMultisig(
          nameBytes,
          roleSigners,
          threshold,
          roleBasedApproval,
          roleApprovals
        )
        .accounts({
          multisigAccount: multisigKeypair.publicKey,
          creator: publicKey,
          paymentAccount: paymentAccountPda,
          systemProgram: SystemProgram.programId,
        })
        .signers([multisigKeypair])
        .rpc();

      return {
        signature: tx,
        multisigPublicKey: multisigKeypair.publicKey.toString(),
      };
    } catch (err: unknown) {
      const errorMessage = (err as Error)?.message || 'Failed to create multisig account';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [getProgram, publicKey]);

  // Fetch multisig account data
  const fetchMultisig = useCallback(async (multisigPublicKey: string): Promise<MultiSigAccount | null> => {
    const program = getProgram();
    if (!program) return null;

    try {
      const multisigPubkey = new PublicKey(multisigPublicKey);
      // Try different possible account names
      if ('multiSigAccount' in program.account) {
        const account = await (program.account as any).multiSigAccount.fetch(multisigPubkey);
        return account as MultiSigAccount;
      } else if ('multisigAccount' in program.account) {
        const account = await (program.account as any).multisigAccount.fetch(multisigPubkey);
        return account as MultiSigAccount;
      }
      throw new Error('MultiSig account not found in program');
    } catch (err: unknown) {
      console.error('Failed to fetch multisig account:', err);
      return null;
    }
  }, [getProgram]);

  // Get all multisig accounts (this would need to be implemented with getProgramAccounts)
  const fetchAllMultisigs = useCallback(async (): Promise<{ publicKey: string; account: MultiSigAccount }[]> => {
    const program = getProgram();
    if (!program) return [];

    try {
      // Try different possible account names
      if ('multiSigAccount' in program.account) {
        const accounts = await (program.account as any).multiSigAccount.all();
        return accounts.map((acc: { publicKey: PublicKey; account: MultiSigAccount }) => ({
          publicKey: acc.publicKey.toString(),
          account: acc.account as MultiSigAccount,
        }));
      } else if ('multisigAccount' in program.account) {
        const accounts = await (program.account as any).multisigAccount.all();
        return accounts.map((acc: { publicKey: PublicKey; account: MultiSigAccount }) => ({
          publicKey: acc.publicKey.toString(),
          account: acc.account as MultiSigAccount,
        }));
      }
      throw new Error('MultiSig account not found in program');
    } catch (err: unknown) {
      console.error('Failed to fetch multisig accounts:', err);
      return [];
    }
  }, [getProgram]);

  // Check if current user is a signer on the multisig
  const isUserSigner = useCallback((multisig: MultiSigAccount): boolean => {
    if (!publicKey) return false;
    
    return multisig.signers.some(signer => 
      signer.signer.toString() === publicKey.toString()
    );
  }, [publicKey]);

  // Get user's role in the multisig
  const getUserRole = useCallback((multisig: MultiSigAccount): number | null => {
    if (!publicKey) return null;
    
    const signer = multisig.signers.find(s => 
      s.signer.toString() === publicKey.toString()
    );
    
    return signer ? signer.role : null;
  }, [publicKey]);

  return {
    createMultisig,
    fetchMultisig,
    fetchAllMultisigs,
    isUserSigner,
    getUserRole,
    loading,
    error,
    ROLES,
  };
};
