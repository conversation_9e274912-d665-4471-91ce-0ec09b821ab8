import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { devUtils } from '../../utils/devtools';

export const WalletButton: React.FC = () => {
  const { publicKey, connected } = useWallet();
  const [balance, setBalance] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (connected && publicKey) {
      loadBalance();
    }
  }, [connected, publicKey]);

  const loadBalance = async () => {
    if (!publicKey) return;
    try {
      const bal = await devUtils.getBalance(publicKey);
      setBalance(bal);
    } catch (error) {
      console.error('Failed to load balance:', error);
    }
  };

  const handleAirdrop = async () => {
    if (!publicKey) return;
    setLoading(true);
    try {
      await devUtils.airdropSol(publicKey, 2);
      await loadBalance(); // Refresh balance
    } catch (error) {
      console.error('Airdrop failed:', error);
      alert('Airdrop failed. Make sure test validator is running.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center space-x-4">
      <WalletMultiButton />
      {connected && publicKey && (
        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-600">
            <div>Connected: {publicKey.toString().slice(0, 8)}...{publicKey.toString().slice(-8)}</div>
            <div>Balance: {balance.toFixed(3)} SOL</div>
          </div>
          {balance < 1 && (
            <button
              onClick={handleAirdrop}
              disabled={loading}
              className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Airdropping...' : 'Airdrop 2 SOL'}
            </button>
          )}
        </div>
      )}
    </div>
  );
};
