import React from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';

export const WalletButton: React.FC = () => {
  const { publicKey, connected } = useWallet();

  return (
    <div className="flex items-center space-x-4">
      <WalletMultiButton />
      {connected && publicKey && (
        <div className="text-sm text-gray-600">
          Connected: {publicKey.toString().slice(0, 8)}...{publicKey.toString().slice(-8)}
        </div>
      )}
    </div>
  );
};
