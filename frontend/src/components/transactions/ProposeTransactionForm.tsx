import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import { useTransactions } from '../../hooks/useTransactions';

interface Props {
  multisigPublicKey: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export const ProposeTransactionForm: React.FC<Props> = ({ 
  multisigPublicKey, 
  onSuccess, 
  onCancel 
}) => {
  const { publicKey } = useWallet();
  const { proposeTransaction, loading, error } = useTransactions();
  
  const [formData, setFormData] = useState({
    destination: '',
    amount: '',
    expiresInHours: '24',
    memo: '',
  });

  const validateForm = (): string | null => {
    if (!formData.destination.trim()) return 'Destination address is required';
    if (!formData.amount.trim()) return 'Amount is required';
    if (!formData.memo.trim()) return 'Memo is required';
    
    try {
      new PublicKey(formData.destination);
    } catch {
      return 'Invalid destination address';
    }

    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      return 'Amount must be a positive number';
    }

    const hours = parseInt(formData.expiresInHours);
    if (isNaN(hours) || hours < 1 || hours > 168) { // Max 1 week
      return 'Expiry must be between 1 and 168 hours';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }

    if (!publicKey) {
      alert('Wallet not connected');
      return;
    }

    try {
      const amountLamports = Math.floor(parseFloat(formData.amount) * 1e9); // Convert SOL to lamports
      const expiresInSeconds = parseInt(formData.expiresInHours) * 3600; // Convert hours to seconds
      
      await proposeTransaction(
        multisigPublicKey,
        formData.destination,
        amountLamports,
        expiresInSeconds,
        formData.memo
      );
      
      onSuccess();
    } catch (err) {
      console.error('Failed to propose transaction:', err);
    }
  };

  const fillCurrentWallet = () => {
    if (publicKey) {
      setFormData({ ...formData, destination: publicKey.toString() });
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">Propose New Transaction</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Destination */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Destination Address
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              value={formData.destination}
              onChange={(e) => setFormData({ ...formData, destination: e.target.value })}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Solana wallet address"
            />
            <button
              type="button"
              onClick={fillCurrentWallet}
              className="px-3 py-2 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              Use Current
            </button>
          </div>
        </div>

        {/* Amount */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Amount (SOL)
          </label>
          <input
            type="number"
            step="0.001"
            min="0"
            value={formData.amount}
            onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="0.001"
          />
          <p className="text-sm text-gray-500 mt-1">
            Minimum: 0.001 SOL
          </p>
        </div>

        {/* Expiry */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Expires In (Hours)
          </label>
          <select
            value={formData.expiresInHours}
            onChange={(e) => setFormData({ ...formData, expiresInHours: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="1">1 Hour</option>
            <option value="6">6 Hours</option>
            <option value="24">24 Hours</option>
            <option value="72">3 Days</option>
            <option value="168">1 Week</option>
          </select>
        </div>

        {/* Memo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Transaction Memo
          </label>
          <input
            type="text"
            value={formData.memo}
            onChange={(e) => setFormData({ ...formData, memo: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., Payment for services"
            maxLength={32}
          />
          <p className="text-sm text-gray-500 mt-1">
            Maximum 32 characters
          </p>
        </div>

        {/* Actions */}
        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Proposing...' : 'Propose Transaction'}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};
