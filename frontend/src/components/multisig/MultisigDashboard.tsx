import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { MultisigList } from './MultisigList';
import { CreateMultisigForm } from './CreateMultisigForm';
import type { MultiSigAccount } from '../../types/anchor';
import { programUtils } from '../../utils/anchor';

interface MultisigItem {
  publicKey: string;
  account: MultiSigAccount;
}

export const MultisigDashboard: React.FC = () => {
  const { connected } = useWallet();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedMultisig, setSelectedMultisig] = useState<MultisigItem | null>(null);

  const handleSelectMultisig = (multisig: MultisigItem) => {
    setSelectedMultisig(multisig);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
  };

  const handleBackToList = () => {
    setSelectedMultisig(null);
  };

  if (!connected) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Connect Your Wallet</h2>
        <p className="text-gray-600 mb-6">
          Please connect your wallet to manage multisig accounts
        </p>
      </div>
    );
  }

  if (showCreateForm) {
    return (
      <CreateMultisigForm
        onSuccess={handleCreateSuccess}
        onCancel={() => setShowCreateForm(false)}
      />
    );
  }

  if (selectedMultisig) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <button
            onClick={handleBackToList}
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <span className="mr-1">←</span> Back to List
          </button>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {programUtils.bytes32ToString(selectedMultisig.account.name)}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Multisig Address</h3>
              <p className="font-mono text-sm break-all">{selectedMultisig.publicKey}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Approval Threshold</h3>
              <p>{selectedMultisig.account.threshold} of {selectedMultisig.account.signers.length} signers</p>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Signers</h3>
            <div className="bg-gray-50 rounded-md p-4">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Public Key</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {selectedMultisig.account.signers.map((signer, index) => {
                    let roleName = "Unknown";
                    switch (signer.role) {
                      case 0: roleName = "Owner"; break;
                      case 1: roleName = "Manager"; break;
                      case 2: roleName = "Finance"; break;
                    }
                    
                    return (
                      <tr key={index}>
                        <td className="px-3 py-2 text-sm text-gray-900">{roleName}</td>
                        <td className="px-3 py-2 text-sm font-mono text-gray-500">{signer.signer.toString()}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {selectedMultisig.account.roleBasedApproval && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Role-based Approvals</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Approvals</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {selectedMultisig.account.minApprovalsPerRole.map((roleApproval, index) => {
                      let roleName = "Unknown";
                      switch (roleApproval.role) {
                        case 0: roleName = "Owner"; break;
                        case 1: roleName = "Manager"; break;
                        case 2: roleName = "Finance"; break;
                      }
                      
                      return (
                        <tr key={index}>
                          <td className="px-3 py-2 text-sm text-gray-900">{roleName}</td>
                          <td className="px-3 py-2 text-sm text-gray-500">{roleApproval.minApprovals}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {selectedMultisig.account.velocityControl && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Velocity Controls</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedMultisig.account.velocityControl.hourlyLimit > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">Hourly Limit:</span>
                      <p className="text-sm font-medium">
                        {(selectedMultisig.account.velocityControl.hourlyLimit / 1e9).toFixed(3)} SOL
                      </p>
                    </div>
                  )}
                  
                  {selectedMultisig.account.velocityControl.dailyLimit > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">Daily Limit:</span>
                      <p className="text-sm font-medium">
                        {(selectedMultisig.account.velocityControl.dailyLimit / 1e9).toFixed(3)} SOL
                      </p>
                    </div>
                  )}
                  
                  {selectedMultisig.account.velocityControl.weeklyLimit > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">Weekly Limit:</span>
                      <p className="text-sm font-medium">
                        {(selectedMultisig.account.velocityControl.weeklyLimit / 1e9).toFixed(3)} SOL
                      </p>
                    </div>
                  )}
                  
                  {selectedMultisig.account.velocityControl.monthlyLimit > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">Monthly Limit:</span>
                      <p className="text-sm font-medium">
                        {(selectedMultisig.account.velocityControl.monthlyLimit / 1e9).toFixed(3)} SOL
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-col md:flex-row gap-4 mt-8">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Propose Transaction
            </button>
            <button className="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200">
              View Pending Transactions
            </button>
            <button className="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200">
              Manage Velocity Controls
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold text-gray-900">Multisig Accounts</h1>
        <button
          onClick={() => setShowCreateForm(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Create New Multisig
        </button>
      </div>

      <MultisigList onSelectMultisig={handleSelectMultisig} />
    </div>
  );
};
