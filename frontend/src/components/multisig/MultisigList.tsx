import React, { useState, useEffect } from 'react';
import { useMultisig } from '../../hooks/useMultisig';
import type { MultiSigAccount } from '../../types/anchor';
import { programUtils, ROLES } from '../../utils/anchor';

interface MultisigItem {
  publicKey: string;
  account: MultiSigAccount;
}

interface Props {
  onSelectMultisig: (multisig: MultisigItem) => void;
}

export const MultisigList: React.FC<Props> = ({ onSelectMultisig }) => {
  const { fetchAllMultisigs, isUserSigner, getUserRole } = useMultisig();
  const [multisigs, setMultisigs] = useState<MultisigItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMultisigs();
  }, []);

  const loadMultisigs = async () => {
    setLoading(true);
    try {
      const accounts = await fetchAllMultisigs();
      setMultisigs(accounts);
    } catch (error) {
      console.error('Failed to load multisigs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRoleName = (role: number): string => {
    switch (role) {
      case ROLES.OWNER: return 'Owner';
      case ROLES.MANAGER: return 'Manager';
      case ROLES.FINANCE: return 'Finance';
      default: return `Role ${role}`;
    }
  };

  const formatName = (nameBytes: number[]): string => {
    return programUtils.bytes32ToString(nameBytes);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (multisigs.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 mb-4">No multisig accounts found</div>
        <p className="text-sm text-gray-400">Create your first multisig account to get started</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-gray-900">Your Multisig Accounts</h2>
      
      <div className="grid gap-4">
        {multisigs.map((multisig) => {
          const userRole = getUserRole(multisig.account);
          const isSigner = isUserSigner(multisig.account);
          
          return (
            <div
              key={multisig.publicKey}
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onSelectMultisig(multisig)}
            >
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="font-medium text-gray-900">
                    {formatName(multisig.account.name)}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {multisig.publicKey.slice(0, 8)}...{multisig.publicKey.slice(-8)}
                  </p>
                </div>
                
                {isSigner && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {userRole !== null ? getRoleName(userRole) : 'Signer'}
                  </span>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Threshold:</span>
                  <span className="ml-2 font-medium">
                    {multisig.account.threshold} of {multisig.account.signers.length}
                  </span>
                </div>
                
                <div>
                  <span className="text-gray-500">Role-based:</span>
                  <span className="ml-2 font-medium">
                    {multisig.account.roleBasedApproval ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="text-sm text-gray-500">Signers:</div>
                <div className="mt-1 flex flex-wrap gap-2">
                  {multisig.account.signers.map((signer, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700"
                    >
                      {getRoleName(signer.role)} - {signer.signer.toString().slice(0, 6)}...
                    </span>
                  ))}
                </div>
              </div>

              {multisig.account.velocityControl && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <div className="text-sm text-gray-500 mb-1">Velocity Controls:</div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    {multisig.account.velocityControl.hourlyLimit > 0 && (
                      <div>Hourly: {(multisig.account.velocityControl.hourlyLimit / 1e9).toFixed(3)} SOL</div>
                    )}
                    {multisig.account.velocityControl.dailyLimit > 0 && (
                      <div>Daily: {(multisig.account.velocityControl.dailyLimit / 1e9).toFixed(3)} SOL</div>
                    )}
                    {multisig.account.velocityControl.weeklyLimit > 0 && (
                      <div>Weekly: {(multisig.account.velocityControl.weeklyLimit / 1e9).toFixed(3)} SOL</div>
                    )}
                    {multisig.account.velocityControl.monthlyLimit > 0 && (
                      <div>Monthly: {(multisig.account.velocityControl.monthlyLimit / 1e9).toFixed(3)} SOL</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      <button
        onClick={loadMultisigs}
        className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Refresh
      </button>
    </div>
  );
};
