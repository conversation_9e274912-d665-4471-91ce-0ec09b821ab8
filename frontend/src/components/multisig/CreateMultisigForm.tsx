import React, { useState } from 'react';
import { useMultisig } from '../../hooks/useMultisig';
import { PublicKey } from '@solana/web3.js';
import { ROLES } from '../../utils/anchor';

interface Signer {
  publicKey: string;
  role: number;
}

interface Props {
  onSuccess: () => void;
  onCancel: () => void;
}

export const CreateMultisigForm: React.FC<Props> = ({ onSuccess, onCancel }) => {
  const { createMultisig, loading, error } = useMultisig();
  
  const [formData, setFormData] = useState({
    name: '',
    threshold: 2,
    roleBasedApproval: false,
  });
  
  const [signers, setSigners] = useState<Signer[]>([
    { publicKey: '', role: ROLES.OWNER },
    { publicKey: '', role: ROLES.MANAGER },
  ]);

  const [roleApprovals, setRoleApprovals] = useState([
    { role: ROLES.OWNER, minApprovals: 1 },
    { role: ROLES.MANAGER, minApprovals: 1 },
  ]);

  const addSigner = () => {
    setSigners([...signers, { publicKey: '', role: ROLES.FINANCE }]);
  };

  const removeSigner = (index: number) => {
    if (signers.length > 2) {
      setSigners(signers.filter((_, i) => i !== index));
    }
  };

  const updateSigner = (index: number, field: keyof Signer, value: string | number) => {
    const updated = [...signers];
    updated[index] = { ...updated[index], [field]: value };
    setSigners(updated);
  };

  const updateRoleApproval = (roleIndex: number, minApprovals: number) => {
    const updated = [...roleApprovals];
    updated[roleIndex] = { ...updated[roleIndex], minApprovals };
    setRoleApprovals(updated);
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) return 'Name is required';
    if (formData.threshold < 1) return 'Threshold must be at least 1';
    if (formData.threshold > signers.length) return 'Threshold cannot exceed number of signers';
    
    for (let i = 0; i < signers.length; i++) {
      const signer = signers[i];
      if (!signer.publicKey.trim()) return `Signer ${i + 1} public key is required`;
      
      try {
        new PublicKey(signer.publicKey);
      } catch {
        return `Signer ${i + 1} has invalid public key`;
      }
    }

    // Check for duplicate signers
    const publicKeys = signers.map(s => s.publicKey);
    const uniqueKeys = new Set(publicKeys);
    if (uniqueKeys.size !== publicKeys.length) {
      return 'Duplicate signers are not allowed';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }

    try {
      await createMultisig(
        formData.name,
        signers,
        formData.threshold,
        formData.roleBasedApproval,
        formData.roleBasedApproval ? roleApprovals : []
      );
      
      onSuccess();
    } catch (err) {
      console.error('Failed to create multisig:', err);
    }
  };

  const getRoleName = (role: number): string => {
    switch (role) {
      case ROLES.OWNER: return 'Owner';
      case ROLES.MANAGER: return 'Manager';
      case ROLES.FINANCE: return 'Finance';
      default: return `Role ${role}`;
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">Create New Multisig Account</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Info */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Multisig Name
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., Company Treasury"
            maxLength={32}
          />
        </div>

        {/* Signers */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Signers
          </label>
          <div className="space-y-3">
            {signers.map((signer, index) => (
              <div key={index} className="flex gap-3 items-center">
                <div className="flex-1">
                  <input
                    type="text"
                    value={signer.publicKey}
                    onChange={(e) => updateSigner(index, 'publicKey', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Public Key"
                  />
                </div>
                <div className="w-32">
                  <select
                    value={signer.role}
                    onChange={(e) => updateSigner(index, 'role', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={ROLES.OWNER}>Owner</option>
                    <option value={ROLES.MANAGER}>Manager</option>
                    <option value={ROLES.FINANCE}>Finance</option>
                  </select>
                </div>
                {signers.length > 2 && (
                  <button
                    type="button"
                    onClick={() => removeSigner(index)}
                    className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-md"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={addSigner}
            className="mt-3 px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md"
          >
            + Add Signer
          </button>
        </div>

        {/* Threshold */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Approval Threshold
          </label>
          <input
            type="number"
            min="1"
            max={signers.length}
            value={formData.threshold}
            onChange={(e) => setFormData({ ...formData, threshold: parseInt(e.target.value) })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p className="text-sm text-gray-500 mt-1">
            Number of signatures required to approve transactions
          </p>
        </div>

        {/* Role-based Approval */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.roleBasedApproval}
              onChange={(e) => setFormData({ ...formData, roleBasedApproval: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm font-medium text-gray-700">
              Enable Role-based Approvals
            </span>
          </label>
          <p className="text-sm text-gray-500 mt-1">
            Require specific roles to approve transactions
          </p>
        </div>

        {/* Role Approval Settings */}
        {formData.roleBasedApproval && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Minimum Approvals per Role
            </label>
            <div className="space-y-2">
              {roleApprovals.map((roleApproval, index) => (
                <div key={index} className="flex items-center gap-3">
                  <span className="w-20 text-sm text-gray-600">
                    {getRoleName(roleApproval.role)}:
                  </span>
                  <input
                    type="number"
                    min="0"
                    max="10"
                    value={roleApproval.minApprovals}
                    onChange={(e) => updateRoleApproval(index, parseInt(e.target.value))}
                    className="w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Multisig'}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};
