import React from 'react';
import { WalletContextProvider } from './components/wallet/WalletProvider';
import { WalletButton } from './components/wallet/WalletButton';
import { MultisigDashboard } from './components/multisig/MultisigDashboard';
import './App.css';

function App() {
  return (
    <WalletContextProvider>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">
                  Multisig Payment System
                </h1>
              </div>
              <WalletButton />
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <MultisigDashboard />
          </div>
        </main>
      </div>
    </WalletContextProvider>
  );
}

export default App;
