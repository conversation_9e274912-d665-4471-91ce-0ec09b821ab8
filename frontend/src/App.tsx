import React from 'react';
import { WalletContextProvider } from './components/wallet/WalletProvider';
import { WalletButton } from './components/wallet/WalletButton';
import './App.css';

function App() {
  return (
    <WalletContextProvider>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">
                  Multisig Payment System
                </h1>
              </div>
              <WalletButton />
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Welcome to Multisig Payment System
                </h2>
                <p className="text-gray-600 mb-6">
                  Connect your wallet to get started with multisig transactions
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>✅ Advanced Multisig with Role-based Approvals</p>
                  <p>✅ Velocity Controls (Hourly/Daily/Weekly/Monthly)</p>
                  <p>✅ Transaction Lifecycle Management</p>
                  <p>✅ Fee Management System</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </WalletContextProvider>
  );
}

export default App;
