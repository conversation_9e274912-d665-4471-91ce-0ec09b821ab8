/**
 * Program IDL in camelCase format in order to be used in JS/TS.
 *
 * Note that this is only a type helper and is not the actual IDL. The original
 * IDL can be found at `target/idl/payment_program.json`.
 */
export type PaymentProgram = {
  "address": "CDJfjkmASLweH8BPuBAMRufJGCsHSgwhJcZXfjgZ7Fne",
  "metadata": {
    "name": "paymentProgram",
    "version": "0.1.0",
    "spec": "0.1.0",
    "description": "Created with Anchor"
  },
  "instructions": [
    {
      "name": "approveTransaction",
      "discriminator": [224, 39, 88, 181, 36, 59, 155, 122],
      "accounts": [
        {
          "name": "approver",
          "writable": true,
          "signer": true
        },
        {
          "name": "multisigAccount",
          "writable": true
        }
      ],
      "args": [
        {
          "name": "txId",
          "type": "u64"
        },
        {
          "name": "memo",
          "type": {
            "array": ["u8", 32]
          }
        }
      ]
    },
    {
      "name": "cancelTransaction",
      "discriminator": [95, 129, 237, 240, 8, 49, 223, 132],
      "accounts": [
        {
          "name": "canceller",
          "writable": true,
          "signer": true
        },
        {
          "name": "multisigAccount",
          "writable": true
        }
      ],
      "args": [
        {
          "name": "txId",
          "type": "u64"
        },
        {
          "name": "memo",
          "type": {
            "array": ["u8", 32]
          }
        }
      ]
    },
    {
      "name": "deposit",
      "discriminator": [242, 35, 198, 137, 82, 225, 242, 182],
      "accounts": [
        {
          "name": "user",
          "writable": true,
          "signer": true
        },
        {
          "name": "paymentAccount",
          "writable": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "amount",
          "type": "u64"
        }
      ]
    }
  ],
  "accounts": [
    {
      "name": "feeVault",
      "discriminator": [181, 157, 91, 112, 90, 59, 231, 16]
    },
    {
      "name": "multiSigAccount",
      "discriminator": [211, 8, 232, 43, 2, 152, 117, 119]
    },
    {
      "name": "paymentAccount",
      "discriminator": [217, 155, 40, 6, 158, 190, 10, 54]
    }
  ],
  "types": [
    {
      "name": "roleSigner",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "signer",
            "type": "pubkey"
          },
          {
            "name": "role",
            "type": "u8"
          }
        ]
      }
    },
    {
      "name": "velocityControl",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "hourlyLimit",
            "type": "u64"
          },
          {
            "name": "dailyLimit",
            "type": "u64"
          },
          {
            "name": "weeklyLimit",
            "type": "u64"
          },
          {
            "name": "monthlyLimit",
            "type": "u64"
          }
        ]
      }
    }
  ]
};
