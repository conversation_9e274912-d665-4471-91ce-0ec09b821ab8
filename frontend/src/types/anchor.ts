import { IdlAccounts, IdlTypes } from '@coral-xyz/anchor';
import { PaymentProgram } from './payment_program';

// Export the IDL type
export type { PaymentProgram };

// Account types
export type MultiSigAccount = IdlAccounts<PaymentProgram>['multiSigAccount'];
export type PaymentAccount = IdlAccounts<PaymentProgram>['paymentAccount'];
export type FeeVault = IdlAccounts<PaymentProgram>['feeVault'];

// Custom types from IDL
export type RoleSigner = IdlTypes<PaymentProgram>['RoleSigner'];
export type RoleApproval = IdlTypes<PaymentProgram>['RoleApproval'];
export type VelocityControl = IdlTypes<PaymentProgram>['VelocityControl'];
export type PendingTransaction = IdlTypes<PaymentProgram>['PendingTransaction'];
export type SignatureLog = IdlTypes<PaymentProgram>['SignatureLog'];
export type TransactionStatus = IdlTypes<PaymentProgram>['TransactionStatus'];

// Instruction argument types
export type InitializeMultisigArgs = {
  name: number[];
  signers: RoleSigner[];
  threshold: number;
  roleBasedApproval: boolean;
  minApprovalsPerRole: RoleApproval[];
};

export type ProposeTransactionArgs = {
  amount: number;
  expiresInSeconds: number;
  memo: number[];
};

export type SetVelocityControlArgs = {
  hourlyLimit: number;
  dailyLimit: number;
  weeklyLimit: number;
  monthlyLimit: number;
};

// Program constants
export const PROGRAM_ID = 'CDJfjkmASLweH8BPuBAMRufJGCsHSgwhJcZXfjgZ7Fne';

// Role constants (matching your backend)
export const ROLES = {
  OWNER: 0,
  MANAGER: 1,
  FINANCE: 2,
} as const;

export type Role = typeof ROLES[keyof typeof ROLES];
