import { PublicKey } from '@solana/web3.js';
import type { PaymentProgram } from './payment_program';

// Export the IDL type
export type { PaymentProgram };

// Define types manually based on your backend
export type RoleSigner = {
  signer: PublicKey;
  role: number;
};

export type RoleApproval = {
  role: number;
  minApprovals: number;
};

export type VelocityControl = {
  hourlyLimit: number;
  dailyLimit: number;
  weeklyLimit: number;
  monthlyLimit: number;
  currentHourSpent: number;
  currentDaySpent: number;
  currentWeekSpent: number;
  currentMonthSpent: number;
  lastHourReset: number;
  lastDayReset: number;
  lastWeekReset: number;
  lastMonthReset: number;
};

export type PendingTransaction = {
  id: number;
  proposer: PublicKey;
  destination: PublicKey;
  amount: number;
  expiresAt: number;
  memo: number[];
  approvals: PublicKey[];
  executed: boolean;
  cancelled: boolean;
};

export type SignatureLog = {
  signer: PublicKey;
  timestamp: number;
  memo: number[];
};

export type MultiSigAccount = {
  name: number[];
  signers: RoleSigner[];
  threshold: number;
  roleBasedApproval: boolean;
  minApprovalsPerRole: RoleApproval[];
  nextTxId: number;
  pendingTransactions: PendingTransaction[];
  signatureLogs: SignatureLog[];
  velocityControl: VelocityControl | null;
};

export type PaymentAccount = {
  owner: PublicKey;
  balance: number;
  frozen: boolean;
  dailySpendLimit: number;
  dailySpent: number;
  lastSpendReset: number;
};

export type FeeVault = {
  authority: PublicKey;
  feeRate: number;
  totalFeesCollected: number;
};

// Instruction argument types
export type InitializeMultisigArgs = {
  name: number[];
  signers: RoleSigner[];
  threshold: number;
  roleBasedApproval: boolean;
  minApprovalsPerRole: RoleApproval[];
};

export type ProposeTransactionArgs = {
  amount: number;
  expiresInSeconds: number;
  memo: number[];
};

export type SetVelocityControlArgs = {
  hourlyLimit: number;
  dailyLimit: number;
  weeklyLimit: number;
  monthlyLimit: number;
};


