import { PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { connection } from './anchor';

/**
 * Development utilities for testing
 */
export const devUtils = {
  /**
   * Airdrop SOL to a wallet for testing
   */
  airdropSol: async (publicKey: PublicKey, amount: number = 2): Promise<string> => {
    try {
      const signature = await connection.requestAirdrop(
        publicKey,
        amount * LAMPORTS_PER_SOL
      );
      
      // Wait for confirmation
      await connection.confirmTransaction(signature);
      
      console.log(`Airdropped ${amount} SOL to ${publicKey.toString()}`);
      return signature;
    } catch (error) {
      console.error('Airdrop failed:', error);
      throw error;
    }
  },

  /**
   * Get SOL balance of a wallet
   */
  getBalance: async (publicKey: PublicKey): Promise<number> => {
    try {
      const balance = await connection.getBalance(publicKey);
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      console.error('Failed to get balance:', error);
      return 0;
    }
  },

  /**
   * Check if test validator is running
   */
  checkValidator: async (): Promise<boolean> => {
    try {
      const version = await connection.getVersion();
      console.log('Validator version:', version);
      return true;
    } catch (error) {
      console.error('Validator not running:', error);
      return false;
    }
  },
};
