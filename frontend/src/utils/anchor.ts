import { AnchorProvider, Program, setProvider } from '@coral-xyz/anchor';
import { Connection, PublicKey } from '@solana/web3.js';
import { PaymentProgram } from '../types/payment_program';
import idl from '../idl/payment_program.json';

// Program ID from your IDL
export const PROGRAM_ID = 'CDJfjkmASLweH8BPuBAMRufJGCsHSgwhJcZXfjgZ7Fne';

// Solana network configuration
export const NETWORK = 'http://127.0.0.1:8899'; // Local test validator
export const COMMITMENT = 'confirmed';

// Create connection to Solana
export const connection = new Connection(NETWORK, COMMITMENT);

// Program ID as PublicKey
export const programId = new PublicKey(PROGRAM_ID);

/**
 * Get the Anchor program instance
 * @param provider - Anchor provider (wallet + connection)
 * @returns Program instance
 */
export function getProgram(provider: AnchorProvider): Program<any> {
  return new Program(idl as any, provider);
}

/**
 * Setup Anchor provider and program
 * @param wallet - Connected wallet
 * @returns Program instance
 */
export function setupProgram(wallet: any): Program<any> {
  const provider = new AnchorProvider(connection, wallet, {
    commitment: COMMITMENT,
  });

  setProvider(provider);
  return getProgram(provider);
}

/**
 * Utility functions for working with the program
 */
export const programUtils = {
  /**
   * Convert string to byte array for memo fields
   */
  stringToBytes32: (str: string): number[] => {
    const bytes = new TextEncoder().encode(str);
    const result = new Array(32).fill(0);
    for (let i = 0; i < Math.min(bytes.length, 32); i++) {
      result[i] = bytes[i];
    }
    return result;
  },

  /**
   * Convert byte array back to string
   */
  bytes32ToString: (bytes: number[]): string => {
    const validBytes = bytes.filter(b => b !== 0);
    return new TextDecoder().decode(new Uint8Array(validBytes));
  },

  /**
   * Convert string to byte array for name fields
   */
  stringToBytes: (str: string, length: number = 32): number[] => {
    const bytes = new TextEncoder().encode(str);
    const result = new Array(length).fill(0);
    for (let i = 0; i < Math.min(bytes.length, length); i++) {
      result[i] = bytes[i];
    }
    return result;
  },

  /**
   * Generate PDA for payment account
   */
  getPaymentAccountPDA: (userPubkey: PublicKey): [PublicKey, number] => {
    return PublicKey.findProgramAddressSync(
      [Buffer.from('payment-account'), userPubkey.toBuffer()],
      programId
    );
  },

  /**
   * Generate PDA for fee vault
   */
  getFeeVaultPDA: (): [PublicKey, number] => {
    return PublicKey.findProgramAddressSync(
      [Buffer.from('fee-vault')],
      programId
    );
  },
};

/**
 * Error handling utilities
 */
export const errorUtils = {
  /**
   * Parse Anchor program errors
   */
  parseError: (error: any): string => {
    if (error?.error?.errorMessage) {
      return error.error.errorMessage;
    }
    if (error?.message) {
      return error.message;
    }
    return 'Unknown error occurred';
  },

  /**
   * Check if error is a specific program error
   */
  isErrorCode: (error: any, code: string): boolean => {
    return error?.error?.errorCode?.code === code;
  },
};
