# Anchor build artifacts
.anchor
target/
**/*.rs.bk
.DS_Store

# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
# yarn.lock - Keeping this for reproducible builds

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
# Keep example env files
!.env.example

# Solana CLI config
.config/solana/

# Build output
dist/
build/
out/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Testing
coverage/
.nyc_output/

# Keypairs and sensitive information
**/keypair.json
**/*wallet-keypair.json
**/*-keypair.json
**/*.keypair
**/*.pem
**/*.key
**/*.cert

# Solana program deployment artifacts
.crates.toml
.cargo-ok

# Rust specific
Cargo.lock
**/*.rs.bk

# Typescript
*.tsbuildinfo

# Temporary files
*.tmp
*.temp
.cache/

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Anchor specific
.anchor/
test-ledger/
.program/

# Solana program deployment artifacts
blockchain/payment-program/target/
blockchain/payment-program/target/deploy/
blockchain/payment-program/target/idl/
blockchain/payment-program/target/types/
blockchain/payment-program/.anchor/

# Local validator data
test-ledger/
validator-ledger/
**/test-ledger/
**/.anchor/test-ledger/
**/validator-*.log
**/rocksdb/
**/*.log
**/*.sst

# Generated IDL files (but keep frontend IDL)
blockchain/payment-program/target/idl/
!frontend/src/idl/

# Debug files
*.debug
*.dump

# Backup files
*.bak
*.backup
*~

# Blockchain specific
.program-keypair.json
wallet.json
*-keypair.json

# Large test files
blockchain/payment-program/tests/*.ts
blockchain/payment-program/tests/*.js
blockchain/payment-program/tests/fixtures/
# Keep test directory structure
!blockchain/payment-program/tests/.gitkeep
